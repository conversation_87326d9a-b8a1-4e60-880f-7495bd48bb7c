import json
from openai import AzureOpenAI
import time
import os
import tempfile
from typing import List, Dict
from flask import current_app
from config.config import TranslatorBotConfig
from src.backend.blueprints.translator_bot.excel_handler import ExcelHandler


class DirectTranslationService:
    """
    Handles file translation using direct API calls to Azure OpenAI.
    """
    
    def __init__(self, user_id: str, original_filename: str = None):
        self.user_id = user_id
        self.config = TranslatorBotConfig()

        # Create independent upload directory for translator bot
        self.temp_base_dir = os.path.join(tempfile.gettempdir(), 'translator-bot-uploads')
        os.makedirs(self.temp_base_dir, exist_ok=True)
        self.upload_dir = os.path.join(self.temp_base_dir, f'user_{user_id}')
        os.makedirs(self.upload_dir, exist_ok=True)

        # Always set self.original_filename
        if original_filename is not None:
            self.original_filename = original_filename
        else:
            self.original_filename = f"data_{user_id}.xlsx"

        self.excel_path = os.path.join(self.upload_dir, self.original_filename)

        # Initialize Excel handler
        self.excel_handler = ExcelHandler(self.excel_path)

        # Initialize Azure OpenAI client using TranslatorBotConfig
        self.client = AzureOpenAI(
            api_key=self.config.openai_llm_key,
            api_version=self.config.api_version,
            azure_endpoint=self.config.api_llm_endpoint,
        )

        # Store deployment name for later use
        self.deployment_name = self.config.llm_deployed


    def cleanup(self):
        """
        Remove the user's temporary upload directory and all its contents.
        """
        try:
            import shutil
            if os.path.exists(self.upload_dir):
                shutil.rmtree(self.upload_dir)
                current_app.logger.info(f"Cleaned up temp directory for user {self.user_id}")
        except Exception as e:
            current_app.logger.error(f"Failed to clean up temp directory for user {self.user_id}: {e}")
        

    def submit_to_gpt(self, json_obj: Dict, prompt: str, file_context: str = None) -> str:
        """
        Submit a JSON object to Azure GPT with a translation prompt.

        Args:
            json_obj (Dict): JSON object to submit
            prompt (str): Translation prompt to use
            file_context (str, optional): Additional file context to include in the user message
        Returns:
            str: Response from GPT
        """
        start_call = time.time()
        payload_size = len(json.dumps(json_obj))
        items_count = len(json_obj.get('content', {}))

        current_app.logger.info(f"Sending request to OpenAI: {payload_size} characters, {items_count} items")
        current_app.logger.debug(f"Using deployment: {self.deployment_name}")

        try:
            # Prepare the messages
            messages = [
                {"role": "system", "content": prompt},
            ]
            if file_context:
                user_content = f"Additional context for the translation model:\n{file_context}\n\n{json.dumps(json_obj)}"
                current_app.logger.debug("Including file context in request")
            else:
                user_content = json.dumps(json_obj)
            messages.append({"role": "user", "content": user_content})

            current_app.logger.debug(f"Request messages prepared, system prompt length: {len(prompt)} chars")

            # Make the API request using Azure OpenAI client
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=messages,
                temperature=0,
                top_p=0.1,
            )
            duration = time.time() - start_call
            response_content = response.choices[0].message.content
            response_length = len(response_content) if response_content else 0

            current_app.logger.info(f"OpenAI response received in {duration:.2f}s, {response_length} chars")
            current_app.logger.debug(f"Response preview: {response_content[:100]}..." if response_content else "Empty response")

            return response_content
        except Exception as e:
            duration = time.time() - start_call
            error_message = f"Azure OpenAI API request failed after {duration:.2f} seconds: {str(e)}"
            current_app.logger.error(error_message)
            current_app.logger.error(f"Request details - Model: {self.deployment_name}, Items: {items_count}, Size: {payload_size}")
            raise Exception(error_message)
    

    def get_default_prompt(self, target_language: str) -> str:
        """
        Get the default translation prompt for the specified target language.

        Args:
            target_language (str): Target language for translation

        Returns:
            str: Default translation prompt
        """
        return f"""
        You are a professional translator. You will be provided a json structure with words or sentences that require translation.
        Translate each element into clear, accurate {target_language}, preserving technical terminology and meaning.
        Double check the meaning of words and phrases to ensure accuracy and correct context.

        IMPORTANT: Additional context for the translation may be provided in the user message. Use it to improve translation accuracy if present.

        You must format your response as a valid JSON object where the keys are the row numbers and the values are the translated {target_language} text. For example:
        {{"1": "30kg Industrial Washing Machine", "2": "Stainless Steel Dryer with Steam Function", "3": "Commercial Ironing System"}}

        Do not include any explanations or notes outside of this JSON structure.
        """


    # def translate_column(self, column_name: str, target_language: str, max_rows: int = 200, file_context: str = None) -> Dict:
    #     """
    #     Translate a specific column in the Excel file.
    #     Now delegated to ExcelHandler.
    #     """
    #     return self.excel_handler.translate_column(
    #         column_name=column_name,
    #         target_language=target_language,
    #         max_rows=max_rows,
    #         file_context=file_context,
    #         submit_to_gpt=self.submit_to_gpt,
    #         get_default_prompt=self.get_default_prompt
    #     )


    # def translate_multiple_columns(self, column_names: List[str], target_language: str, max_rows: int = 200, file_context: str = None) -> Dict:
    #     """
    #     Translate multiple columns in the Excel file.
    #     Delegated to ExcelHandler.
    #     """
    #     return self.excel_handler.translate_multiple_columns(
    #         column_names=column_names,
    #         target_language=target_language,
    #         max_rows=max_rows,
    #         file_context=file_context,
    #         submit_to_gpt=self.submit_to_gpt,
    #         get_default_prompt=self.get_default_prompt
    #     )
    

    def translate_excel_multiple_languages(self, column_names: List[str], target_languages: List[str], max_rows: int = 200, file_context: str = None, session_id: str = None) -> Dict:
        """
        Translate the specified columns into multiple target languages, saving a new file for each language.
        Now delegated to ExcelHandler.

        Args:
            column_names (List[str]): List of column names or letters to translate
            target_languages (List[str]): List of target languages
            max_rows (int): Maximum rows per batch for API calls
            file_context (str, optional): Additional context for translation
            session_id (str, optional): Session ID for progress tracking

        Returns:
            Dict: Summary with per-language results and file paths
        """
        return self.excel_handler.translate_multiple_languages(
            column_names=column_names,
            target_languages=target_languages,
            max_rows=max_rows,
            file_context=file_context,
            session_id=session_id,
            submit_to_gpt=self.submit_to_gpt,
            get_default_prompt=self.get_default_prompt,
            upload_dir=self.upload_dir,
            user_id=self.user_id
        )


    def get_excel_columns(self) -> List[str]:
        """
        Get the list of columns in the Excel file.
        Delegated to ExcelHandler.
        """
        return self.excel_handler.get_columns()


    def get_excel_info(self) -> Dict:
        """
        Get Excel file information including columns and row count.
        Delegated to ExcelHandler.
        """
        return self.excel_handler.get_info()

    # def preview_translation(self, column_name: str, target_language: str, preview_rows: int = 10, file_context: str = None) -> Dict:
    #     """
    #     Get a preview of translation for a specific column.
    #     Delegated to ExcelHandler.
    #     """
    #     return self.excel_handler.preview_translation(
    #         column_name=column_name,
    #         target_language=target_language,
    #         preview_rows=preview_rows,
    #         file_context=file_context,
    #         submit_to_gpt=self.submit_to_gpt,
    #         get_default_prompt=self.get_default_prompt
    #     )

