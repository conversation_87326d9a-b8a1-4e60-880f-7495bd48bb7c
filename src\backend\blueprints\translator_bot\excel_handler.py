import json
import pandas as pd
from openpyxl import load_workbook
import os
from typing import List, Dict, <PERSON><PERSON>
from flask import current_app


class ExcelHandler:
    """
    Handles Excel file operations for the translation service.
    Separated from the main translation service to focus on Excel-specific functionality.
    """
    
    def __init__(self, excel_path: str):
        """
        Initialize the Excel handler with the path to the Excel file.
        
        Args:
            excel_path (str): Path to the Excel file
        """
        self.excel_path = excel_path
    
    def read_excel_column_to_json(self, column_name: str, max_rows: int = 200) -> Tuple[List[Dict], str]:
        """
        Read a column from an Excel file and transform it into JSON arrays with pagination.
        
        Args:
            column_name (str): Column letter or name to read
            max_rows (int): Maximum number of rows per JSON array (pagination)
            
        Returns:
            Tuple[List[Dict], str]: List of JSON objects and header value
        """
        # Load the Excel file
        df = pd.read_excel(self.excel_path)
        
        # Convert column name to proper format if it's a letter
        if len(column_name) == 1 and column_name.isalpha():
            try:
                column_name = df.columns[ord(column_name.upper()) - ord('A')]
            except IndexError:
                raise ValueError(f"Column {column_name} not found in the Excel file")
        
        # Check if the column exists
        if column_name not in df.columns:
            raise ValueError(f"Column {column_name} not found in the Excel file")
        
        # Get the header
        header = column_name
        
        # Get the column data (excluding header)
        column_data = df[column_name].fillna("").astype(str)
        
        # Create JSON arrays with pagination
        json_arrays = []
        for i in range(0, len(column_data), max_rows):
            chunk = column_data.iloc[i:i+max_rows]
            json_obj = {
                "column": column_name,
                "content": {str(j+1+i): value for j, value in enumerate(chunk)}
            }
            json_arrays.append(json_obj)
        
        return json_arrays, header
    
    def write_results_to_excel(self, results: List[str], output_column: str, header: str):
        """
        Write the results to a new column in the Excel file.

        Args:
            results (List[str]): List of results to write
            output_column (str): Column letter or name to write to (if None, will auto-determine)
            header (str): Header value for the output column
        """
        current_app.logger.info(f"Writing results to Excel - output_column: {output_column}, header: {header}")

        # Load the workbook and get the active sheet
        workbook = load_workbook(self.excel_path)
        sheet = workbook.active

        # Determine the column index
        if output_column is None:
            # Auto-determine the next available column
            df = pd.read_excel(self.excel_path)
            col_idx = len(df.columns) + 1
            current_app.logger.info(f"Auto-determined column index: {col_idx}")
        elif len(output_column) == 1 and output_column.isalpha():
            col_idx = ord(output_column.upper()) - ord('A') + 1
            current_app.logger.info(f"Using letter column {output_column} -> index {col_idx}")
        else:
            # If it's not a single letter, we'll need to find its index
            df = pd.read_excel(self.excel_path)
            if output_column in df.columns:
                col_idx = df.columns.get_loc(output_column) + 1
                current_app.logger.info(f"Found existing column {output_column} -> index {col_idx}")
            else:
                # If the column doesn't exist, create it
                col_idx = len(df.columns) + 1
                current_app.logger.info(f"Creating new column {output_column} -> index {col_idx}")
        
        # Write the header first
        header_text = f"{header}_translated" if header else "Translated"
        sheet.cell(row=1, column=col_idx, value=header_text)
        current_app.logger.info(f"Written header '{header_text}' to column {col_idx}")

        # Write the results to the Excel file, starting from row 2 (after header)
        total_values_written = 0
        for i, result in enumerate(results):
            try:
                # Parse the result as JSON to extract the values
                result_json = json.loads(result)
                current_app.logger.debug(f"Processing result batch {i+1}: {len(result_json)} items")

                # Write each value to the corresponding row (add 1 for header row)
                for row_idx, value in result_json.items():
                    actual_row = int(row_idx) + 1  # +1 for header row
                    sheet.cell(row=actual_row, column=col_idx, value=value)
                    total_values_written += 1

            except json.JSONDecodeError as e:
                current_app.logger.warning(f"Invalid JSON in result batch {i+1}: {e}")
                # If the result is not valid JSON, write it as is
                sheet.cell(row=i+2, column=col_idx, value=result)  # +2 to account for header and 0-indexing
                total_values_written += 1
            except Exception as e:
                current_app.logger.error(f"Error processing result batch {i+1}: {e}")

        current_app.logger.info(f"Written {total_values_written} values to Excel column {col_idx}")

        # Save the workbook
        try:
            workbook.save(self.excel_path)
            current_app.logger.info(f"Successfully saved Excel file: {self.excel_path}")
        except Exception as e:
            current_app.logger.error(f"Error saving Excel file: {e}")
            raise

    def get_excel_columns(self) -> List[str]:
        """
        Get the list of columns in the Excel file.

        Returns:
            List[str]: List of column names
        """
        try:
            df = pd.read_excel(self.excel_path)
            return df.columns.tolist()
        except Exception as e:
            current_app.logger.error(f"Error reading Excel columns: {e}")
            return []

    def get_excel_info(self) -> Dict:
        """
        Get Excel file information including columns and row count.

        Returns:
            Dict: Dictionary containing columns list and row count
        """
        try:
            df = pd.read_excel(self.excel_path)
            return {
                'columns': df.columns.tolist(),
                'row_count': len(df),
                'success': True
            }
        except Exception as e:
            current_app.logger.error(f"Error reading Excel info: {e}")
            return {
                'columns': [],
                'row_count': 0,
                'success': False,
                'error': str(e)
            }

    def get_next_available_column_letter(self) -> str:
        """
        Get the next available column letter for writing results.
        
        Returns:
            str: Next available column letter (e.g., 'C', 'D', etc.)
        """
        try:
            df = pd.read_excel(self.excel_path)
            return chr(ord('A') + len(df.columns))
        except Exception as e:
            current_app.logger.error(f"Error determining next column: {e}")
            return 'B'  # Default fallback
