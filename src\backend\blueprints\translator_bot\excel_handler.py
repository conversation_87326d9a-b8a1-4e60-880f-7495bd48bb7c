import json
import pandas as pd
from openpyxl import load_workbook
import os
import shutil
from typing import List, Dict, Tuple, Callable
from flask import current_app
from src.backend.blueprints.translator_bot.progress_tracker import progress_tracker


class ExcelHandler:
    """
    Handles Excel file operations for the translation service.
    Separated from the main translation service to focus on Excel-specific functionality.
    """
    
    def __init__(self, excel_path: str):
        """
        Initialize the Excel handler with the path to the Excel file.
        
        Args:
            excel_path (str): Path to the Excel file
        """
        self.excel_path = excel_path
    
    def read_excel_column_to_json(self, column_name: str, max_rows: int = 200) -> Tuple[List[Dict], str]:
        """
        Read a column from an Excel file and transform it into JSON arrays with pagination.
        
        Args:
            column_name (str): Column letter or name to read
            max_rows (int): Maximum number of rows per JSON array (pagination)
            
        Returns:
            Tuple[List[Dict], str]: List of JSON objects and header value
        """
        # Load the Excel file
        df = pd.read_excel(self.excel_path)
        
        # Convert column name to proper format if it's a letter
        if len(column_name) == 1 and column_name.isalpha():
            try:
                column_name = df.columns[ord(column_name.upper()) - ord('A')]
            except IndexError:
                raise ValueError(f"Column {column_name} not found in the Excel file")
        
        # Check if the column exists
        if column_name not in df.columns:
            raise ValueError(f"Column {column_name} not found in the Excel file")
        
        # Get the header
        header = column_name
        
        # Get the column data (excluding header)
        column_data = df[column_name].fillna("").astype(str)
        
        # Create JSON arrays with pagination
        json_arrays = []
        for i in range(0, len(column_data), max_rows):
            chunk = column_data.iloc[i:i+max_rows]
            json_obj = {
                "column": column_name,
                "content": {str(j+1+i): value for j, value in enumerate(chunk)}
            }
            json_arrays.append(json_obj)
        
        return json_arrays, header
    
    def write_results_to_excel(self, results: List[str], output_column: str, header: str):
        """
        Write the results to a new column in the Excel file.

        Args:
            results (List[str]): List of results to write
            output_column (str): Column letter or name to write to (if None, will auto-determine)
            header (str): Header value for the output column
        """
        current_app.logger.info(f"Writing results to Excel - output_column: {output_column}, header: {header}")

        # Load the workbook and get the active sheet
        workbook = load_workbook(self.excel_path)
        sheet = workbook.active

        # Determine the column index
        if output_column is None:
            # Auto-determine the next available column
            df = pd.read_excel(self.excel_path)
            col_idx = len(df.columns) + 1
            current_app.logger.info(f"Auto-determined column index: {col_idx}")
        elif len(output_column) == 1 and output_column.isalpha():
            col_idx = ord(output_column.upper()) - ord('A') + 1
            current_app.logger.info(f"Using letter column {output_column} -> index {col_idx}")
        else:
            # If it's not a single letter, we'll need to find its index
            df = pd.read_excel(self.excel_path)
            if output_column in df.columns:
                col_idx = df.columns.get_loc(output_column) + 1
                current_app.logger.info(f"Found existing column {output_column} -> index {col_idx}")
            else:
                # If the column doesn't exist, create it
                col_idx = len(df.columns) + 1
                current_app.logger.info(f"Creating new column {output_column} -> index {col_idx}")
        
        # Write the header first
        header_text = f"{header}_translated" if header else "Translated"
        sheet.cell(row=1, column=col_idx, value=header_text)
        current_app.logger.info(f"Written header '{header_text}' to column {col_idx}")

        # Write the results to the Excel file, starting from row 2 (after header)
        total_values_written = 0
        for i, result in enumerate(results):
            try:
                # Parse the result as JSON to extract the values
                result_json = json.loads(result)
                current_app.logger.debug(f"Processing result batch {i+1}: {len(result_json)} items")

                # Write each value to the corresponding row (add 1 for header row)
                for row_idx, value in result_json.items():
                    actual_row = int(row_idx) + 1  # +1 for header row
                    sheet.cell(row=actual_row, column=col_idx, value=value)
                    total_values_written += 1

            except json.JSONDecodeError as e:
                current_app.logger.warning(f"Invalid JSON in result batch {i+1}: {e}")
                # If the result is not valid JSON, write it as is
                sheet.cell(row=i+2, column=col_idx, value=result)  # +2 to account for header and 0-indexing
                total_values_written += 1
            except Exception as e:
                current_app.logger.error(f"Error processing result batch {i+1}: {e}")

        current_app.logger.info(f"Written {total_values_written} values to Excel column {col_idx}")

        # Save the workbook
        try:
            workbook.save(self.excel_path)
            current_app.logger.info(f"Successfully saved Excel file: {self.excel_path}")
        except Exception as e:
            current_app.logger.error(f"Error saving Excel file: {e}")
            raise

    def get_excel_columns(self) -> List[str]:
        """
        Get the list of columns in the Excel file.

        Returns:
            List[str]: List of column names
        """
        try:
            df = pd.read_excel(self.excel_path)
            return df.columns.tolist()
        except Exception as e:
            current_app.logger.error(f"Error reading Excel columns: {e}")
            return []

    def get_excel_info(self) -> Dict:
        """
        Get Excel file information including columns and row count.

        Returns:
            Dict: Dictionary containing columns list and row count
        """
        try:
            df = pd.read_excel(self.excel_path)
            return {
                'columns': df.columns.tolist(),
                'row_count': len(df),
                'success': True
            }
        except Exception as e:
            current_app.logger.error(f"Error reading Excel info: {e}")
            return {
                'columns': [],
                'row_count': 0,
                'success': False,
                'error': str(e)
            }

    def get_next_available_column_letter(self) -> str:
        """
        Get the next available column letter for writing results.

        Returns:
            str: Next available column letter (e.g., 'C', 'D', etc.)
        """
        try:
            df = pd.read_excel(self.excel_path)
            return chr(ord('A') + len(df.columns))
        except Exception as e:
            current_app.logger.error(f"Error determining next column: {e}")
            return 'B'  # Default fallback

    def get_columns(self) -> List[str]:
        """
        Get the list of columns in the Excel file.
        Alias for get_excel_columns for consistency.

        Returns:
            List[str]: List of column names
        """
        return self.get_excel_columns()

    def get_info(self) -> Dict:
        """
        Get Excel file information including columns and row count.
        Alias for get_excel_info for consistency.

        Returns:
            Dict: Dictionary containing columns list and row count
        """
        return self.get_excel_info()

    def translate_column(self, column_name: str, target_language: str, max_rows: int = 200,
                        file_context: str = None, submit_to_gpt: Callable = None,
                        get_default_prompt: Callable = None) -> Dict:
        """
        Translate a specific column in the Excel file.

        Args:
            column_name (str): Column name or letter to translate
            target_language (str): Target language for translation
            max_rows (int): Maximum rows per batch for API calls
            file_context (str, optional): Additional context for translation
            submit_to_gpt (Callable): Function to submit data to GPT
            get_default_prompt (Callable): Function to get default prompt

        Returns:
            Dict: Translation result with success status and details
        """
        current_app.logger.info(f"Starting translation of column '{column_name}' to {target_language}")

        try:
            # Read column data in batches
            json_arrays, header = self.read_excel_column_to_json(column_name, max_rows)
            current_app.logger.info(f"Column {column_name} split into {len(json_arrays)} batches")

            # Get translation prompt
            prompt = get_default_prompt(target_language)

            # Process each batch
            batch_results = []
            for batch_idx, json_obj in enumerate(json_arrays, 1):
                current_app.logger.info(f"Processing batch {batch_idx}/{len(json_arrays)}")

                try:
                    response = submit_to_gpt(json_obj, prompt, file_context=file_context)
                    batch_results.append(response)
                    current_app.logger.info(f"Successfully processed batch {batch_idx}")
                except Exception as e:
                    current_app.logger.error(f"Error in GPT translation for batch {batch_idx}: {e}")
                    batch_results.append(json.dumps({}))

            # Write results to Excel
            self.write_results_to_excel(batch_results, None, header)

            return {
                'success': True,
                'batches_processed': len(batch_results),
                'column': column_name,
                'target_language': target_language
            }

        except Exception as e:
            current_app.logger.error(f"Error translating column {column_name}: {e}")
            return {
                'success': False,
                'error': str(e),
                'column': column_name,
                'target_language': target_language
            }

    def translate_multiple_columns(self, column_names: List[str], target_language: str,
                                  max_rows: int = 200, file_context: str = None,
                                  submit_to_gpt: Callable = None, get_default_prompt: Callable = None) -> Dict:
        """
        Translate multiple columns in the Excel file.

        Args:
            column_names (List[str]): List of column names or letters to translate
            target_language (str): Target language for translation
            max_rows (int): Maximum rows per batch for API calls
            file_context (str, optional): Additional context for translation
            submit_to_gpt (Callable): Function to submit data to GPT
            get_default_prompt (Callable): Function to get default prompt

        Returns:
            Dict: Translation result with success status and details for each column
        """
        current_app.logger.info(f"Starting translation of {len(column_names)} columns to {target_language}")

        results = []
        successful_columns = 0

        for column_name in column_names:
            current_app.logger.info(f"Processing column: {column_name}")

            result = self.translate_column(
                column_name=column_name,
                target_language=target_language,
                max_rows=max_rows,
                file_context=file_context,
                submit_to_gpt=submit_to_gpt,
                get_default_prompt=get_default_prompt
            )

            results.append(result)
            if result['success']:
                successful_columns += 1

        return {
            'success': successful_columns > 0,
            'columns_processed': len(column_names),
            'successful_columns': successful_columns,
            'target_language': target_language,
            'results': results
        }

    def translate_multiple_languages(self, column_names: List[str], target_languages: List[str],
                                   max_rows: int = 200, file_context: str = None, session_id: str = None,
                                   submit_to_gpt: Callable = None, get_default_prompt: Callable = None,
                                   upload_dir: str = None, user_id: str = None) -> Dict:
        """
        Translate the specified columns into multiple target languages, saving a new file for each language.

        Args:
            column_names (List[str]): List of column names or letters to translate
            target_languages (List[str]): List of target languages
            max_rows (int): Maximum rows per batch for API calls
            file_context (str, optional): Additional context for translation
            session_id (str, optional): Session ID for progress tracking
            submit_to_gpt (Callable): Function to submit data to GPT
            get_default_prompt (Callable): Function to get default prompt
            upload_dir (str): Directory to save translated files
            user_id (str): User ID for logging

        Returns:
            Dict: Summary with per-language results and file paths
        """
        current_app.logger.info(f"Starting translation for user {user_id}")
        current_app.logger.info(f"Columns to translate: {column_names}")
        current_app.logger.info(f"Target languages: {target_languages}")
        current_app.logger.info(f"Max rows per batch: {max_rows}")
        current_app.logger.info(f"File context provided: {'Yes' if file_context else 'No'}")

        results = []
        excel_files = []
        original_excel_path = self.excel_path

        # Calculate total batches for progress tracking
        total_batches = 0
        if session_id:
            # Calculate total batches by checking each column
            for col in column_names:
                try:
                    json_arrays, _ = self.read_excel_column_to_json(col, max_rows)
                    total_batches += len(json_arrays) * len(target_languages)
                except Exception as e:
                    current_app.logger.warning(f"Could not calculate batches for column {col}: {e}")

            progress_tracker.create_session(session_id, total_batches, "Starting translation...")
            current_app.logger.info(f"Created progress session {session_id} with {total_batches} total batches")

        completed_batches = 0
        original_filename = os.path.basename(original_excel_path)
        original_base, original_ext = os.path.splitext(original_filename)

        current_app.logger.info(f"Processing file: {original_filename}")
        current_app.logger.info(f"File path: {original_excel_path}")

        return self._process_languages_for_translation(
            target_languages, column_names, original_base, original_ext, upload_dir,
            max_rows, file_context, session_id, submit_to_gpt, get_default_prompt,
            completed_batches, results, excel_files, user_id
        )

    def _process_languages_for_translation(self, target_languages: List[str], column_names: List[str],
                                         original_base: str, original_ext: str, upload_dir: str,
                                         max_rows: int, file_context: str, session_id: str,
                                         submit_to_gpt: Callable, get_default_prompt: Callable,
                                         completed_batches: int, results: List, excel_files: List,
                                         user_id: str) -> Dict:
        """
        Process each target language for translation.
        """
        original_excel_path = self.excel_path

        for lang_idx, lang in enumerate(target_languages, 1):
            current_app.logger.info(f"Processing language {lang_idx}/{len(target_languages)}: {lang}")

            # Prepare a new file for this language, using original filename + language suffix
            lang_file_name = f"{original_base}_{lang}{original_ext}"
            lang_file = os.path.join(upload_dir, lang_file_name)
            current_app.logger.info(f"Creating language file: {lang_file}")

            try:
                shutil.copy2(original_excel_path, lang_file)
                current_app.logger.info(f"Successfully copied original file to {lang_file}")
            except Exception as e:
                current_app.logger.error(f"Failed to copy file for language {lang}: {e}")
                lang_result = {
                    'success': False,
                    'error': f'Failed to create file for language {lang}: {str(e)}',
                    'output_file': lang_file
                }
                results.append({
                    'language': lang,
                    'result': lang_result
                })
                continue

            # Process this language file
            completed_batches = self._process_single_language_file(
                lang_file, lang, column_names, max_rows, file_context, session_id,
                submit_to_gpt, get_default_prompt, completed_batches, results, user_id
            )

            excel_files.append(lang_file)
            current_app.logger.info(f"Completed processing language {lang}")

        return self._finalize_translation_results(results, target_languages, session_id)

    def _process_single_language_file(self, lang_file: str, lang: str, column_names: List[str],
                                    max_rows: int, file_context: str, session_id: str,
                                    submit_to_gpt: Callable, get_default_prompt: Callable,
                                    completed_batches: int, results: List, user_id: str) -> int:
        """
        Process a single language file by translating all specified columns.
        """
        # Temporarily point to the new file
        prev_excel_path = self.excel_path
        self.excel_path = lang_file

        try:
            for col_idx, col in enumerate(column_names, 1):
                current_app.logger.info(f"Processing column {col_idx}/{len(column_names)} for {lang}: {col}")

                # For each column, translate in batches
                try:
                    json_arrays, _ = self.read_excel_column_to_json(col, max_rows)
                    current_app.logger.info(f"Column {col} split into {len(json_arrays)} batches")
                except Exception as e:
                    current_app.logger.error(f"Error reading column {col} for language {lang}: {e}")
                    lang_result = {
                        'success': False,
                        'error': f'Error reading column {col}: {str(e)}',
                        'output_file': lang_file
                    }
                    results.append({
                        'language': lang,
                        'column': col,
                        'result': lang_result
                    })
                    continue

                batch_results = []
                for batch_idx, json_obj in enumerate(json_arrays, 1):
                    current_app.logger.info(f"Processing batch {batch_idx}/{len(json_arrays)} for column {col} in {lang}")

                    # Use the same prompt as in translate_column
                    prompt = get_default_prompt(lang)
                    try:
                        current_app.logger.debug(f"Sending batch {batch_idx} to GPT with {len(json_obj.get('content', {}))} items")
                        response = submit_to_gpt(json_obj, prompt, file_context=file_context)
                        batch_results.append(response)
                        current_app.logger.info(f"Successfully processed batch {batch_idx}/{len(json_arrays)}")

                        # Update progress
                        if session_id:
                            completed_batches += 1
                            progress_details = f"Processing '{col}' -> {lang} - Batch {batch_idx}/{len(json_arrays)}"
                            progress_tracker.update_progress(session_id, completed=completed_batches, details=progress_details)

                    except Exception as e:
                        current_app.logger.error(f"Error in GPT translation for batch {batch_idx}: {e}")
                        batch_results.append(json.dumps({}))

                        # Still update progress even on error
                        if session_id:
                            completed_batches += 1
                            progress_details = f"Error in {lang} - Column {col} - Batch {batch_idx}/{len(json_arrays)}"
                            progress_tracker.update_progress(session_id, completed=completed_batches, details=progress_details)

                # Write results to Excel for this column
                try:
                    current_app.logger.info(f"Writing {len(batch_results)} batch results to Excel for column {col}")
                    self.write_results_to_excel(batch_results, None, col)
                    lang_result = {
                        'success': True,
                        'batches_processed': len(batch_results),
                        'output_file': lang_file,
                        'original_column': col,
                        'target_language': lang
                    }
                    current_app.logger.info(f"Successfully wrote results for column {col} in {lang}")
                except Exception as e:
                    current_app.logger.error(f"Error writing results for column {col} in {lang}: {e}")
                    lang_result = {
                        'success': False,
                        'error': f'Error writing results for column {col}: {str(e)}',
                        'output_file': lang_file
                    }
                results.append({
                    'language': lang,
                    'column': col,
                    'result': lang_result
                })

        except Exception as e:
            current_app.logger.error(f"Error processing language {lang}: {e}")
            lang_result = {
                'success': False,
                'error': str(e),
                'output_file': lang_file
            }
            results.append({
                'language': lang,
                'result': lang_result
            })
        finally:
            # Restore original excel_path
            self.excel_path = prev_excel_path

        return completed_batches

    def _finalize_translation_results(self, results: List, target_languages: List[str], session_id: str) -> Dict:
        """
        Finalize translation results and complete progress tracking.
        """
        # Calculate success statistics
        successful_results = [r for r in results if r['result']['success']]
        total_results = len(results)
        success_rate = len(successful_results) / total_results if total_results > 0 else 0

        current_app.logger.info(f"Translation completed: {len(successful_results)}/{total_results} successful ({success_rate:.1%})")

        summary = {
            'success': any(r['result']['success'] for r in results),
            'languages': target_languages,
            'results': results,
        }

        current_app.logger.info(f"Translation summary: {summary['success']}")

        # Complete progress tracking
        if session_id:
            if summary['success']:
                progress_tracker.complete_session(session_id, "Translation completed successfully")
            else:
                progress_tracker.complete_session(session_id, "Translation completed with errors")

        return summary

    # def preview_translation(self, column_name: str, target_language: str, preview_rows: int = 10,
    #                        file_context: str = None, submit_to_gpt: Callable = None,
    #                        get_default_prompt: Callable = None) -> Dict:
    #     """
    #     Get a preview of translation for a specific column.

    #     Args:
    #         column_name (str): Column name or letter to preview
    #         target_language (str): Target language for translation
    #         preview_rows (int): Number of rows to preview
    #         file_context (str, optional): Additional context for translation
    #         submit_to_gpt (Callable): Function to submit data to GPT
    #         get_default_prompt (Callable): Function to get default prompt

    #     Returns:
    #         Dict: Preview result with success status and preview data
    #     """
    #     current_app.logger.info(f"Starting preview translation of column '{column_name}' to {target_language}")

    #     try:
    #         # Load the Excel file
    #         df = pd.read_excel(self.excel_path)

    #         # Convert column name to proper format if it's a letter
    #         if len(column_name) == 1 and column_name.isalpha():
    #             try:
    #                 column_name = df.columns[ord(column_name.upper()) - ord('A')]
    #             except IndexError:
    #                 raise ValueError(f"Column {column_name} not found in the Excel file")

    #         # Check if the column exists
    #         if column_name not in df.columns:
    #             raise ValueError(f"Column {column_name} not found in the Excel file")

    #         # Get the column data (excluding header)
    #         column_data = df[column_name].fillna("").astype(str)

    #         # Take only the first preview_rows for preview
    #         preview_data = column_data.head(preview_rows)

    #         # Create JSON object for preview
    #         json_obj = {
    #             "column": column_name,
    #             "content": {str(i+1): value for i, value in enumerate(preview_data)}
    #         }

    #         # Get translation prompt
    #         prompt = get_default_prompt(target_language)

    #         # Submit to GPT for translation
    #         response = submit_to_gpt(json_obj, prompt, file_context=file_context)

    #         # Parse the response
    #         try:
    #             translated_data = json.loads(response)
    #         except json.JSONDecodeError:
    #             raise Exception("Invalid JSON response from translation service")

    #         # Create preview data structure
    #         preview_items = []
    #         for i, original_value in enumerate(preview_data):
    #             row_key = str(i + 1)
    #             translated_value = translated_data.get(row_key, "Translation failed")
    #             preview_items.append({
    #                 'row': i + 1,
    #                 'original': original_value,
    #                 'translated': translated_value
    #             })

    #         return {
    #             'success': True,
    #             'preview_data': preview_items,
    #             'total_rows_in_file': len(column_data),
    #             'column': column_name,
    #             'target_language': target_language
    #         }

    #     except Exception as e:
    #         current_app.logger.error(f"Error in preview translation for column {column_name}: {e}")
    #         return {
    #             'success': False,
    #             'error': str(e),
    #             'column': column_name,
    #             'target_language': target_language
    #         }
