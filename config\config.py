from configparser import RawConfigParser
from os import environ, getlogin
from urllib.parse import quote_plus

from utils.core import Singleton, get_relative_path

DEFAULT_CONFIG_PATH = get_relative_path(__file__, "../config.ini")


class PrimaryConfig(metaclass=Singleton):
    def __init__(self) -> None:
        # Default config file
        config_file = DEFAULT_CONFIG_PATH
        self.config_env = ''

        # Check overrides from local config_xyz.ini files
        env = environ.get("FLASK_ENV")
        username = getlogin().lower()

        if env == "PRD":
            config_file = "config_prd.ini"
            self.config_env = ''
        elif env == "STG":
            config_file = "config_stg.ini"
            self.config_env = 'STAGING'
        elif env == "DEV":
            config_file = "config_dev.ini"
            self.config_env = 'DEVEL'
        elif username:
            config_file = f"config_{username}.ini"
            self.config_env = 'LOCAL'

        self.config = RawConfigParser(allow_no_value=True)
        config_file = get_relative_path(__file__, "../" + config_file)
        self.config.read([DEFAULT_CONFIG_PATH, config_file])

    @property
    def env(self) -> str:
        return self.config_env


class SubordinateConfig:
    def __init__(self, module: str) -> None:
        self.__module = module
        self.primary_config = PrimaryConfig()

    def get_setting(self, name: str, is_in_env: bool = False) -> str:
        vars = environ if is_in_env else None
        return self.primary_config.config.get(
            self.__module, name, vars=vars, fallback=None
        )


class CommonConfig(metaclass=Singleton):
    def __init__(self) -> None:
        self.flask = SubordinateConfig("web")
        self.common = SubordinateConfig("common")
        self.log = SubordinateConfig("log")
        self.ad = SubordinateConfig("ad")
        self.db = SubordinateConfig("db")
        self.azure_openai = SubordinateConfig("azure_openai")

    @property
    def flask_app(self) -> str:
        return self.flask.get_setting("flask_app")

    @property
    def flask_debug(self) -> int:
        return int(self.flask.get_setting("flask_debug"))

    @property
    def flask_secret_key(self) -> str:
        return self.flask.get_setting("FLASK_SECRET_KEY", True)

    @property
    def pride_product_root(self) -> str:
        return self.flask.get_setting("PRIDE_PRODUCT_ROOT", True)

    @property
    def default_run_mode(self) -> str:
        return self.common.get_setting("run_mode")

    @property
    def default_version(self) -> str:
        return str(self.common.get_setting("version_gittag") + ' ' + self.common.get_setting("version_gitrev"))

    @property
    def default_version_release(self) -> str:
        return str(self.common.get_setting("version_release"))

    @property
    def default_logout_url(self) -> str:
        return self.common.get_setting("logout_url")

    @property
    def log_level(self) -> str:
        return self.log.get_setting("loglevel")

    @property
    def log_folder(self) -> str:
        return self.log.get_setting("log_folder")

    @property
    def ad_client_id(self) -> str:
        return self.ad.get_setting("client_id")

    @property
    def ad_authority_uri(self) -> str:
        return self.ad.get_setting("authority_uri")

    @property
    def ad_redirect_uri(self) -> str:
        return self.ad.get_setting("redirect_uri")

    @property
    def ad_scope(self) -> str:
        return self.ad.get_setting("ad_scope")

    @property
    def ad_endpoint(self) -> str:
        return self.ad.get_setting("ad_endpoint_api")

    @property
    def ad_schema_callback(self) -> str:
        return self.ad.get_setting("ad_schema_callback")

    @property
    def ad_secret(self) -> str:
        return self.ad.get_setting("AD_SECRET", True)

    @property
    def db_name(self) -> str:
        return self.db.get_setting("db_name")

    @property
    def db_host(self) -> str:
        return self.db.get_setting("db_host")

    @property
    def db_port(self) -> str:
        return self.db.get_setting("db_port")

    @property
    def db_user(self) -> str:
        return self.db.get_setting("db_user")

    @property
    def db_driver(self) -> str:
        return self.db.get_setting("db_driver")

    @property
    def db_password(self) -> str:
        return quote_plus(self.db.get_setting("DB_PASSWORD", True))

    @property
    def proxy(self) -> str:
        return self.common.get_setting("proxy")

    @property
    def api_version(self) -> str:
        return self.azure_openai.get_setting("api_version")

    @property
    def api_llm_endpoint(self) -> str:
        return self.azure_openai.get_setting("api_llm_endpoint")

    @property
    def llm_deployed(self) -> str:
        return self.azure_openai.get_setting("llm_deployed")

    @property
    def openai_llm_key(self) -> str:
        return self.azure_openai.get_setting("OPENAI_LLM_KEY", True)


class RAGConfig(CommonConfig, metaclass=Singleton):
    def __init__(self) -> None:
        super().__init__()
        self.__call_center_bot_storage = SubordinateConfig("call_center_bot.rag.storage")
        self.__call_center_bot_fetcher = SubordinateConfig("call_center_bot.rag.fetcher")
        self.__how_to_bot_storage = SubordinateConfig("how_to_bot.rag.storage")
        self.__how_to_bot_fetcher = SubordinateConfig("how_to_bot.rag.fetcher")
        self.__jdanallo_storage = SubordinateConfig("jdanallo.rag.storage")
        self.__jdanallo_fetcher = SubordinateConfig("jdanallo.rag.fetcher")
        self.__jdanallo_parameters = SubordinateConfig("jdanallo.rag.param")
        self.__seobot_fetcher = SubordinateConfig("seobot.rag.fetcher")
        self.__seobot_storage = SubordinateConfig("seobot.rag.storage")
        self.__seobot_parameters = SubordinateConfig("seobot.rag.param")
        self.__loader = SubordinateConfig("rag.loader")
        self.__generator = SubordinateConfig("rag.generator")
        self.__parameters = SubordinateConfig("rag.parameters")

    @property
    def call_center_bot_storage_endpoint(self) -> str:
        return self.__call_center_bot_storage.get_setting("endpoint")

    @property
    def call_center_bot_storage_index_name(self) -> str:
        return self.__call_center_bot_storage.get_setting("index_name")

    @property
    def how_to_bot_storage_endpoint(self) -> str:
        return self.__how_to_bot_storage.get_setting("endpoint")

    @property
    def how_to_bot_storage_index_name(self) -> str:
        return self.__how_to_bot_storage.get_setting("index_name")

    @property
    def call_center_bot_storage_key(self) -> str:
        return self.__call_center_bot_storage.get_setting("CALL_CENTER_BOT_RAG_AZURESEARCH_ADMIN_KEY", True)

    @property
    def how_to_bot_storage_key(self) -> str:
        return self.__how_to_bot_storage.get_setting("HOW_TO_BOT_RAG_AZURESEARCH_ADMIN_KEY", True)

    @property
    def jdanallo_storage_endpoint(self) -> str:
        return self.__jdanallo_storage.get_setting("endpoint")

    @property
    def jdanallo_storage_index_name(self) -> str:
        return self.__jdanallo_storage.get_setting("index_name")

    @property
    def jdanallo_storage_key(self) -> str:
        return self.__jdanallo_storage.get_setting("JDANALLO_RAG_AZURESEARCH_ADMIN_KEY", True)

    @property
    def seobot_storage_endpoint(self) -> str:
        return self.__seobot_storage.get_setting("endpoint")

    @property
    def seobot_storage_index_name(self) -> str:
        return self.__seobot_storage.get_setting("index_name")

    @property
    def seobot_storage_key(self) -> str:
        return self.__seobot_storage.get_setting("SEOBOT_RAG_AZURESEARCH_ADMIN_KEY", True)

    @property
    def storage_vector_profile(self) -> str:
        return self.__parameters.get_setting("vector_search_profile")

    @property
    def jde_storage_vector_profile(self) -> str:
        return self.__jdanallo_parameters.get_setting("vector_search_profile")

    @property
    def seo_storage_vector_profile(self) -> str:
        return self.__seobot_parameters.get_setting("vector_search_profile")

    @property
    def storage_vector_algorithm(self) -> str:
        return self.__parameters.get_setting("vector_algorithm")

    @property
    def storage_semantic_reranker(self) -> str:
        return self.__parameters.get_setting("semantic_configuration")

    @property
    def jde_storage_semantic_reranker(self) -> str:
        return self.__jdanallo_parameters.get_setting("semantic_configuration")

    @property
    def seo_storage_semantic_reranker(self) -> str:
        return self.__seobot_parameters.get_setting("semantic_configuration")

    @property
    def storage_embedding_model(self) -> str:
        return self.__parameters.get_setting("embedding_model")

    @property
    def storage_embedding_endpoint(self) -> str:
        return self.__parameters.get_setting("embedding_resource")

    @property
    def storage_embedding_version(self) -> str:
        return self.__parameters.get_setting("embedding_api_version")

    @property
    def storage_embedding_key(self) -> str:
        return self.__parameters.get_setting("RAG_EMBEDDING_KEY", True)

    @property
    def storage_field_id_name(self) -> str:
        return self.__parameters.get_setting("AZURESEARCH_FIELDS_ID", True)

    @property
    def storage_field_content_name(self) -> str:
        return self.__parameters.get_setting("AZURESEARCH_FIELDS_CONTENT", True)

    @property
    def storage_field_vector_name(self) -> str:
        return self.__parameters.get_setting("AZURESEARCH_FIELDS_CONTENT_VECTOR", True)

    @property
    def storage_field_metadata_name(self) -> str:
        return self.__parameters.get_setting("AZURESEARCH_FIELDS_TAG", True)

    @property
    def generator_endpoint(self) -> str:
        return self.__generator.get_setting("endpoint")

    @property
    def generator_key(self) -> str:
        return self.__generator.get_setting("RAG_GENERATOR_KEY", True)

    @property
    def generator_temperature(self) -> float:
        return float(self.__generator.get_setting("temperature"))

    @property
    def generator_model(self) -> str:
        return self.__generator.get_setting("model")

    @property
    def generator_version(self) -> str:
        return self.__generator.get_setting("version")

    @property
    def call_center_bot_local_root(self) -> str:
        return self.__call_center_bot_fetcher.get_setting("local_root")

    @property
    def call_center_bot_pride_products_root(self) -> str:
        return self.__call_center_bot_fetcher.get_setting("pride_products_root")

    @property
    def call_center_bot_pride_document_types(self) -> str:
        return self.__call_center_bot_fetcher.get_setting("pride_document_types")

    @property
    def how_to_bot_local_root(self) -> str:
        return self.__how_to_bot_fetcher.get_setting("local_root")

    @property
    def how_to_bot_pride_products_root(self) -> str:
        return self.__how_to_bot_fetcher.get_setting("pride_products_root")

    @property
    def jdanallo_local_root(self) -> str:
        return self.__jdanallo_fetcher.get_setting("local_root")

    @property
    def jdanallo_pride_products_root(self) -> str:
        return self.__jdanallo_fetcher.get_setting("pride_products_root")

    @property
    def seobot_local_root(self) -> str:
        return self.__seobot_fetcher.get_setting("local_root")

    @property
    def jdanallo_pride_products_root(self) -> str:
        return self.__seobot_fetcher.get_setting("pride_products_root")

    @property
    def document_intelligence_key(self) -> str:
        return self.__loader.get_setting("RAG_DOCUMENT_INTELLIGENCE_KEY", True)

    @property
    def document_intelligence_endpoint(self) -> str:
        return self.__loader.get_setting("document_intelligence_endpoint")

    @property
    def temporary_pdf(self) -> str:
        return self.__generator.get_setting("temporary_pdf")


class Text2SQLConfig(CommonConfig, metaclass=Singleton):
    def __init__(self) -> None:
        super().__init__()
        self.__oci = SubordinateConfig("txt2sql.oracle_oci")
        self.__openai = SubordinateConfig("txt2sql.azure_openai")
        self.__compli_bot_search = SubordinateConfig("compli_bot.txt2sql.azure_ai_search")
        self.__call_center_bot_search = SubordinateConfig("call_center_bot.txt2sql.azure_ai_search")
        self.__preliminary = SubordinateConfig("txt2sql.preliminary")

    @property
    def oci_client_path(self) -> str:
        return self.__oci.get_setting("oci_client_path")

    @property
    def oci_dsn(self) -> str:
        return self.__oci.get_setting("oci_dsn")

    @property
    def oci_schema(self) -> str:
        schema = self.__oci.get_setting("oci_schema")
        if schema:
            schema = schema + "." if len(schema) > 0 else schema
        return schema

    @property
    def oci_username(self) -> str:
        return self.__oci.get_setting("OCI_USERNAME", True)

    @property
    def oci_password(self) -> str:
        return self.__oci.get_setting("OCI_PASSWORD", True)

    @property
    def openai_api_version(self) -> str:
        return self.__openai.get_setting("api_version")

    @property
    def openai_embedding_endpoint(self) -> str:
        return self.__openai.get_setting("api_embedding_endpoint")

    @property
    def openai_inquiry_embedder(self) -> str:
        return self.__openai.get_setting("inquiry_embedder")

    @property
    def openai_llm_endpoint(self) -> str:
        return self.__openai.get_setting("api_llm_endpoint")

    @property
    def openai_llm_deployed(self) -> str:
        return self.__openai.get_setting("llm_deployed")

    @property
    def openai_embedding_key(self) -> str:
        return self.__openai.get_setting("OPENAI_EMBEDDING_KEY", True)

    @property
    def openai_llm_key(self) -> str:
        return self.__openai.get_setting("OPENAI_LLM_KEY", True)

    @property
    def compli_bot_search_index(self) -> str:
        return self.__compli_bot_search.get_setting("search_index")

    @property
    def call_center_bot_search_index(self) -> str:
        return self.__call_center_bot_search.get_setting("search_index")

    @property
    def compli_bot_search_endpoint(self) -> str:
        return self.__compli_bot_search.get_setting("search_endpoint")

    @property
    def call_center_bot_search_endpoint(self) -> str:
        return self.__call_center_bot_search.get_setting("search_endpoint")

    @property
    def call_center_bot_search_query_key(self) -> str:
        return self.__call_center_bot_search.get_setting("CALL_CENTER_BOT_SEARCH_QUERY_KEY", True)

    @property
    def compli_bot_search_query_key(self) -> str:
        return self.__compli_bot_search.get_setting("COMPLI_BOT_SEARCH_QUERY_KEY", True)

    @property
    def call_center_bot_search_admin_key(self) -> str:
        return self.__call_center_bot_search.get_setting("CALL_CENTER_BOT_SEARCH_ADMIN_KEY", True)

    @property
    def compli_bot_search_admin_key(self) -> str:
        return self.__compli_bot_search.get_setting("COMPLI_BOT_SEARCH_ADMIN_KEY", True)

    @property
    def preliminary_model_deployed(self) -> str:
        return self.__preliminary.get_setting("model_deployed")

    @property
    def preliminary_key(self) -> str:
        return self.__preliminary.get_setting("PRELIMINARY_KEY", True)


class BlobStorageConfig(CommonConfig, metaclass=Singleton):
    def __init__(self) -> None:
        super().__init__()
        self.__call_center_bot_blob_storage_credentials = SubordinateConfig("call_center_bot.azure_blob_storage")
        self.__how_to_bot_blob_storage_credentials = SubordinateConfig("how_to_bot.azure_blob_storage")
        self.__jdanallo_blob_storage_credentials = SubordinateConfig("jdanallo.azure_blob_storage")
        self.__seobot_blob_storage_credentials = SubordinateConfig("seobot.azure_blob_storage")

    @property
    def call_center_bot_account_name(self) -> str:
        return self.__call_center_bot_blob_storage_credentials.get_setting("account_name")

    @property
    def call_center_bot_account_url(self) -> str:
        return self.__call_center_bot_blob_storage_credentials.get_setting("account_url")

    @property
    def call_center_bot_table_container(self) -> str:
        return self.__call_center_bot_blob_storage_credentials.get_setting("table_container")

    @property
    def call_center_bot_container_image(self) -> str:
        return self.__call_center_bot_blob_storage_credentials.get_setting("image_container")

    @property
    def call_center_bot_account_key(self) -> str:
        return self.__call_center_bot_blob_storage_credentials.get_setting("CALL_CENTER_BOT_BLOB_STORAGE_KEY", True)

    @property
    def how_to_bot_account_name(self) -> str:
        return self.__how_to_bot_blob_storage_credentials.get_setting("account_name")

    @property
    def how_to_bot_account_url(self) -> str:
        return self.__how_to_bot_blob_storage_credentials.get_setting("account_url")

    @property
    def how_to_bot_table_container(self) -> str:
        return self.__how_to_bot_blob_storage_credentials.get_setting("table_container")

    @property
    def how_to_bot_container_image(self) -> str:
        return self.__how_to_bot_blob_storage_credentials.get_setting("image_container")

    @property
    def how_to_bot_account_key(self) -> str:
        return self.__how_to_bot_blob_storage_credentials.get_setting("HOW_TO_BOT_BLOB_STORAGE_KEY", True)

    @property
    def jdanallo_account_name(self) -> str:
        return self.__jdanallo_blob_storage_credentials.get_setting("account_name")

    @property
    def jdanallo_account_url(self) -> str:
        return self.__jdanallo_blob_storage_credentials.get_setting("account_url")

    @property
    def jdanallo_table_container(self) -> str:
        return self.__jdanallo_blob_storage_credentials.get_setting("table_container")

    @property
    def jdanallo_container_image(self) -> str:
        return self.__jdanallo_blob_storage_credentials.get_setting("image_container")

    @property
    def jdanallo_account_key(self) -> str:
        return self.__jdanallo_blob_storage_credentials.get_setting("JDANALLO_BLOB_STORAGE_KEY", True)

    @property
    def seobot_account_name(self) -> str:
        return self.__seobot_blob_storage_credentials.get_setting("account_name")

    @property
    def seobot_account_url(self) -> str:
        return self.__seobot_blob_storage_credentials.get_setting("account_url")

    @property
    def seobot_table_container(self) -> str:
        return self.__seobot_blob_storage_credentials.get_setting("table_container")

    @property
    def seobot_container_image(self) -> str:
        return self.__seobot_blob_storage_credentials.get_setting("image_container")

    @property
    def seobot_account_key(self) -> str:
        return self.__seobot_blob_storage_credentials.get_setting("SEOBOT_BLOB_STORAGE_KEY", True)


class EproExcelLaConfig(CommonConfig, metaclass=Singleton):
    def __init__(self) -> None:
        super().__init__()
        self.__openai = SubordinateConfig("translator.azure_ai_api")

    @property
    def translator_azure_ai_api_version(self) -> str:
        return self.__openai.get_setting("api_version")

    @property
    def translator_azure_ai_api_llm_endpoint(self) -> str:
        return self.__openai.get_setting("api_llm_endpoint")

    @property
    def translator_azure_ai_api_llm_deployed(self) -> str:
        return self.__openai.get_setting("llm_deployed")

    @property
    def translator_azure_ai_api_translator_endpoint(self) -> str:
        return self.__openai.get_setting("api_translator_endpoint")

    @property
    def translator_azure_ai_api_llm_key(self) -> str:
        return self.__openai.get_setting("OPENAI_LLM_KEY", True)

    @property
    def translator_azure_ai_api_translator_key(self) -> str:
        return self.__openai.get_setting("TRANSLATOR_KEY", True)


class TranslatorBotConfig(CommonConfig, metaclass=Singleton):
    """
    Configuration for the translator bot.
    """
    def __init__(self) -> None:
        super().__init__()
        self.__translator = SubordinateConfig("translator.azure_ai_api")

    @property
    def api_version(self) -> str:
        return self.__translator.get_setting("api_version")

    @property
    def api_llm_endpoint(self) -> str:
        return self.__translator.get_setting("api_llm_endpoint")

    @property
    def llm_deployed(self) -> str:
        return self.__translator.get_setting("llm_deployed")

    @property
    def openai_llm_key(self) -> str:
        return self.__translator.get_setting("OPENAI_LLM_KEY", True)